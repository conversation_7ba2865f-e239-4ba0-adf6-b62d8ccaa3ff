{"root": ["./src/app.tsx", "./src/index.tsx", "./src/setuptests.tsx", "./src/types.d.ts", "./src/vite-env.d.ts", "./src/__mocks__/lucide-react.ts", "./src/__mocks__/posthog-js.ts", "./src/__mocks__/pretty-bytes.js", "./src/__mocks__/shiki.ts", "./src/__mocks__/vscrui.ts", "./src/__mocks__/@vscode/webview-ui-toolkit/react.ts", "./src/__mocks__/components/chat/taskheader.tsx", "./src/__mocks__/i18n/translationcontext.tsx", "./src/__mocks__/i18n/setup.ts", "./src/__mocks__/utils/highlighter.ts", "./src/__tests__/app.test.tsx", "./src/__tests__/contextwindowprogress.test.tsx", "./src/__tests__/contextwindowprogresslogic.test.ts", "./src/__tests__/telemetryclient.test.ts", "./src/components/account/accountview.tsx", "./src/components/auth/authenticationrequired.tsx", "./src/components/chat/announcement.tsx", "./src/components/chat/autoapprovemenu.tsx", "./src/components/chat/autoapprovetogglebutton.tsx", "./src/components/chat/autoapprovedrequestlimitwarning.tsx", "./src/components/chat/batchfilepermission.tsx", "./src/components/chat/browsersessionrow.tsx", "./src/components/chat/chatactions.tsx", "./src/components/chat/chatrow.tsx", "./src/components/chat/chattextarea.tsx", "./src/components/chat/chatview.tsx", "./src/components/chat/checkpointwarning.tsx", "./src/components/chat/codebasesearchresult.tsx", "./src/components/chat/codebasesearchresultsdisplay.tsx", "./src/components/chat/commandexecution.tsx", "./src/components/chat/commandexecutionerror.tsx", "./src/components/chat/compactmcpresourcedisplay.tsx", "./src/components/chat/compactmcptooldisplay.tsx", "./src/components/chat/contextcondenserow.tsx", "./src/components/chat/contextmenu.tsx", "./src/components/chat/contextwindowprogress.tsx", "./src/components/chat/diffsummarybar.tsx", "./src/components/chat/feedbackbuttons.tsx", "./src/components/chat/feedbackdialog.tsx", "./src/components/chat/followupsuggest.tsx", "./src/components/chat/iconbutton.tsx", "./src/components/chat/markdown.tsx", "./src/components/chat/mention.tsx", "./src/components/chat/messageusageanalytics.tsx", "./src/components/chat/modelselector.tsx", "./src/components/chat/profileviolationwarning.tsx", "./src/components/chat/progressindicator.tsx", "./src/components/chat/qaptcoderbranding.tsx", "./src/components/chat/reasoningblock.tsx", "./src/components/chat/systempromptwarning.tsx", "./src/components/chat/taskheader.tsx", "./src/components/chat/__tests__/announcement.test.tsx", "./src/components/chat/__tests__/batchfilepermission.test.tsx", "./src/components/chat/__tests__/chattextarea.test.tsx", "./src/components/chat/__tests__/chatview.auto-approve.test.tsx", "./src/components/chat/__tests__/chatview.test.tsx", "./src/components/chat/__tests__/taskheader.test.tsx", "./src/components/chat/checkpoints/checkpointmenu.tsx", "./src/components/chat/checkpoints/checkpointsaved.tsx", "./src/components/chat/checkpoints/schema.ts", "./src/components/common/codeaccordian.tsx", "./src/components/common/codeblock.tsx", "./src/components/common/customspinner.tsx", "./src/components/common/markdownblock.tsx", "./src/components/common/mermaidblock.tsx", "./src/components/common/statusdot.tsx", "./src/components/common/tab.tsx", "./src/components/common/telemetrybanner.tsx", "./src/components/common/thumbnails.tsx", "./src/components/common/tooluseblock.tsx", "./src/components/common/vscodebuttonlink.tsx", "./src/components/common/__mocks__/codeblock.tsx", "./src/components/common/__mocks__/markdownblock.tsx", "./src/components/common/__tests__/codeblock.test.tsx", "./src/components/history/batchdeletechatdialog.tsx", "./src/components/history/copybutton.tsx", "./src/components/history/deletechatdialog.tsx", "./src/components/history/exportbutton.tsx", "./src/components/history/historypreview.tsx", "./src/components/history/historyview.tsx", "./src/components/history/usagestatsbutton.tsx", "./src/components/history/usechatsearch.ts", "./src/components/history/__tests__/historyview.test.tsx", "./src/components/human-relay/humanrelaydialog.tsx", "./src/components/mcp/mcpenabledtoggle.tsx", "./src/components/mcp/mcperrorrow.tsx", "./src/components/mcp/mcpresourcerow.tsx", "./src/components/mcp/mcptoolrow.tsx", "./src/components/mcp/mcpview.tsx", "./src/components/mcp/__tests__/mcptoolrow.test.tsx", "./src/components/modes/modesview.tsx", "./src/components/modes/__tests__/modesview.test.tsx", "./src/components/settings/about.tsx", "./src/components/settings/apiconfigmanager.tsx", "./src/components/settings/apierrormessage.tsx", "./src/components/settings/apikeymanagementsettings.tsx", "./src/components/settings/apikeymanagerpopup.tsx", "./src/components/settings/apioptions.tsx", "./src/components/settings/autoapprovesettings.tsx", "./src/components/settings/autoapprovetoggle.tsx", "./src/components/settings/browsersettings.tsx", "./src/components/settings/checkpointsettings.tsx", "./src/components/settings/codeindexsettings.tsx", "./src/components/settings/concurrentfilereadsexperiment.tsx", "./src/components/settings/contextmanagementsettings.tsx", "./src/components/settings/diffsettingscontrol.tsx", "./src/components/settings/experimentalfeature.tsx", "./src/components/settings/experimentalsettings.tsx", "./src/components/settings/historymanagementsettings.tsx", "./src/components/settings/languagesettings.tsx", "./src/components/settings/mcpsettings.tsx", "./src/components/settings/modesettings.tsx", "./src/components/settings/modeldescriptionmarkdown.tsx", "./src/components/settings/modelinfoview.tsx", "./src/components/settings/modelpicker.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/promptssettings.tsx", "./src/components/settings/r1formatsetting.tsx", "./src/components/settings/ratelimitsecondscontrol.tsx", "./src/components/settings/section.tsx", "./src/components/settings/sectionheader.tsx", "./src/components/settings/settingsview.tsx", "./src/components/settings/temperaturecontrol.tsx", "./src/components/settings/terminalsettings.tsx", "./src/components/settings/thinkingbudget.tsx", "./src/components/settings/constants.ts", "./src/components/settings/styles.ts", "./src/components/settings/transforms.ts", "./src/components/settings/types.ts", "./src/components/settings/__tests__/apiconfigmanager.test.tsx", "./src/components/settings/__tests__/apioptions.test.tsx", "./src/components/settings/__tests__/autoapprovetoggle.test.tsx", "./src/components/settings/__tests__/concurrentfilereadsexperiment.test.tsx", "./src/components/settings/__tests__/contextmanagementsettings.test.tsx", "./src/components/settings/__tests__/modelpicker.test.tsx", "./src/components/settings/__tests__/settingsview.test.tsx", "./src/components/settings/__tests__/temperaturecontrol.test.tsx", "./src/components/settings/__tests__/thinkingbudget.test.tsx", "./src/components/settings/providers/anthropic.tsx", "./src/components/settings/providers/bedrock.tsx", "./src/components/settings/providers/bedrockcustomarn.tsx", "./src/components/settings/providers/chutes.tsx", "./src/components/settings/providers/deepseek.tsx", "./src/components/settings/providers/gemini.tsx", "./src/components/settings/providers/glama.tsx", "./src/components/settings/providers/groq.tsx", "./src/components/settings/providers/lmstudio.tsx", "./src/components/settings/providers/litellm.tsx", "./src/components/settings/providers/mistral.tsx", "./src/components/settings/providers/ollama.tsx", "./src/components/settings/providers/openai.tsx", "./src/components/settings/providers/openaicompatible.tsx", "./src/components/settings/providers/openrouter.tsx", "./src/components/settings/providers/openrouterbalancedisplay.tsx", "./src/components/settings/providers/requesty.tsx", "./src/components/settings/providers/requestybalancedisplay.tsx", "./src/components/settings/providers/unbound.tsx", "./src/components/settings/providers/vscodelm.tsx", "./src/components/settings/providers/vertex.tsx", "./src/components/settings/providers/xai.tsx", "./src/components/settings/providers/index.ts", "./src/components/settings/providers/__tests__/bedrock.test.tsx", "./src/components/settings/utils/headers.ts", "./src/components/settings/utils/organizationfilters.ts", "./src/components/settings/utils/__tests__/headers.test.ts", "./src/components/settings/utils/__tests__/organizationfilters.test.ts", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/autosize-textarea.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/index.ts", "./src/components/ui/input.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/select-dropdown.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/__tests__/select-dropdown.test.tsx", "./src/components/ui/chat/chat.tsx", "./src/components/ui/chat/chatinput.tsx", "./src/components/ui/chat/chatinputprovider.ts", "./src/components/ui/chat/chatmessage.tsx", "./src/components/ui/chat/chatmessageprovider.ts", "./src/components/ui/chat/chatmessages.tsx", "./src/components/ui/chat/chatprovider.ts", "./src/components/ui/chat/index.ts", "./src/components/ui/chat/types.ts", "./src/components/ui/chat/usechatinput.ts", "./src/components/ui/chat/usechatmessage.ts", "./src/components/ui/chat/usechatui.ts", "./src/components/ui/hooks/index.ts", "./src/components/ui/hooks/useclipboard.ts", "./src/components/ui/hooks/useopenrouterkeyinfo.ts", "./src/components/ui/hooks/useopenroutermodelproviders.ts", "./src/components/ui/hooks/userequestykeyinfo.ts", "./src/components/ui/hooks/userooportal.ts", "./src/components/ui/hooks/useroutermodels.ts", "./src/components/ui/hooks/useselectedmodel.ts", "./src/components/ui/hooks/__tests__/useselectedmodel.test.ts", "./src/components/ui/markdown/blockquote.tsx", "./src/components/ui/markdown/codeblock.tsx", "./src/components/ui/markdown/markdown.tsx", "./src/components/ui/markdown/index.ts", "./src/components/user/trialbanner.tsx", "./src/components/user/usagedisplay.tsx", "./src/components/user/usageindicator.tsx", "./src/components/user/usermanagementsettings.tsx", "./src/components/welcome/qapthero.tsx", "./src/components/welcome/rootips.tsx", "./src/components/welcome/welcomeview.tsx", "./src/components/welcome/__tests__/rootips.test.tsx", "./src/context/authcontext.tsx", "./src/context/extensionstatecontext.tsx", "./src/context/__tests__/extensionstatecontext.test.tsx", "./src/hooks/useauthenticationstate.ts", "./src/i18n/translationcontext.tsx", "./src/i18n/setup.ts", "./src/i18n/test-utils.ts", "./src/i18n/__mocks__/translationcontext.tsx", "./src/i18n/__tests__/translationcontext.test.tsx", "./src/lib/utils.ts", "./src/oauth/urls.ts", "./src/stories/autosizetextarea.stories.tsx", "./src/stories/badge.stories.tsx", "./src/stories/button.stories.ts", "./src/stories/chat.stories.tsx", "./src/stories/collapsible.stories.tsx", "./src/stories/combobox.stories.tsx", "./src/stories/dropdownmenu.stories.tsx", "./src/stories/progress.stories.tsx", "./src/stories/slider.stories.tsx", "./src/utils/telemetryclient.ts", "./src/utils/clipboard.ts", "./src/utils/command-validation.ts", "./src/utils/context-mentions.ts", "./src/utils/diffstats.ts", "./src/utils/doclinks.ts", "./src/utils/format.ts", "./src/utils/formatprice.ts", "./src/utils/getlanguagefrompath.ts", "./src/utils/highlight.ts", "./src/utils/highlighter.ts", "./src/utils/mcp.ts", "./src/utils/model-utils.ts", "./src/utils/path-mentions.ts", "./src/utils/removeleadingnonalphanumeric.ts", "./src/utils/textmatetohljs.ts", "./src/utils/usedebounceeffect.ts", "./src/utils/validate.ts", "./src/utils/vscode.ts", "./src/utils/__tests__/telemetryclient.test.ts", "./src/utils/__tests__/command-validation.test.ts", "./src/utils/__tests__/context-mentions.test.ts", "./src/utils/__tests__/format.test.ts", "./src/utils/__tests__/model-utils.test.ts", "./src/utils/__tests__/path-mentions.test.ts", "../src/shared/extensionmessage.ts", "../src/shared/profilevalidator.ts", "../src/shared/telemetrysetting.ts", "../src/shared/webviewmessage.ts", "../src/shared/api.ts", "../src/shared/array.ts", "../src/shared/checkexistapiconfig.ts", "../src/shared/combineapirequests.ts", "../src/shared/combinecommandsequences.ts", "../src/shared/context-mentions.ts", "../src/shared/cost.ts", "../src/shared/embeddingmodels.ts", "../src/shared/experiments.ts", "../src/shared/getapimetrics.ts", "../src/shared/globalfilenames.ts", "../src/shared/language.ts", "../src/shared/mcp.ts", "../src/shared/modes.ts", "../src/shared/package.ts", "../src/shared/safejsonparse.ts", "../src/shared/support-prompt.ts", "../src/shared/tools.ts", "../src/shared/vscodeselectorutils.ts", "../src/shared/__tests__/profilevalidator.test.ts", "../src/shared/__tests__/api.test.ts", "../src/shared/__tests__/checkexistapiconfig.test.ts", "../src/shared/__tests__/combineapirequests.test.ts", "../src/shared/__tests__/combinecommandsequences.test.ts", "../src/shared/__tests__/context-mentions.test.ts", "../src/shared/__tests__/experiments.test.ts", "../src/shared/__tests__/getapimetrics.test.ts", "../src/shared/__tests__/language.test.ts", "../src/shared/__tests__/modes.test.ts", "../src/shared/__tests__/support-prompts.test.ts", "../src/shared/__tests__/vscodeselectorutils.test.ts"], "version": "5.8.3"}